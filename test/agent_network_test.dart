import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:neorevv/src/presentation/screens/agent_network/agent_network_screen.dart';
import 'package:neorevv/src/presentation/cubit/cubit/network_widget_cubit.dart';

void main() {
  group('AgentNetworkScreen Tests', () {
    testWidgets('AgentNetworkScreen should build without errors', (
      WidgetTester tester,
    ) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: AgentNetworkScreen(
            selectedBrokerId: 'test-broker-id',
            showScaffold: true,
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify that the screen builds without errors
      expect(find.byType(AgentNetworkScreen), findsOneWidget);
    });

    testWidgets('AgentNetworkScreen should show loading initially', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: AgentNetworkScreen(
            selectedBrokerId: 'test-broker-id',
            showScaffold: true,
          ),
        ),
      );

      // Should show loading indicator initially
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('AgentNetworkScreen should show hierarchy after loading', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: AgentNetworkScreen(
            selectedBrokerId: 'test-broker-id',
            showScaffold: true,
          ),
        ),
      );

      // Wait for loading to complete
      await tester.pumpAndSettle();

      // Should show the hierarchy title
      expect(find.text('Agent Network Hierarchy'), findsOneWidget);
    });

    test('NetworkWidgetCubit should load hierarchy data', () {
      final cubit = NetworkWidgetCubit();

      // Load hierarchy data
      cubit.loadHierarchyData();

      // Verify that the state is loaded
      expect(cubit.state, isA<NetworkWidgetLoaded>());

      final loadedState = cubit.state as NetworkWidgetLoaded;
      expect(loadedState.broker.name, equals('Charlotte Anderson'));
      expect(loadedState.selectedAgent.name, equals('Ryan Harris'));
      expect(loadedState.isExpanded, isFalse);
      expect(
        loadedState.collapsedLevelsCount,
        equals(2),
      ); // 3 parents - 1 immediate parent
    });

    test('NetworkWidgetCubit should toggle hierarchy expansion', () {
      final cubit = NetworkWidgetCubit();

      // Load hierarchy data first
      cubit.loadHierarchyData();

      // Toggle expansion
      cubit.toggleHierarchyExpansion();

      final state = cubit.state as NetworkWidgetLoaded;
      expect(state.isExpanded, isTrue);

      // Toggle again to collapse
      cubit.toggleHierarchyExpansion();

      final newState = cubit.state as NetworkWidgetLoaded;
      expect(newState.isExpanded, isFalse);
    });

    test('NetworkWidgetCubit should select different agents', () {
      final cubit = NetworkWidgetCubit();

      // Load hierarchy data first
      cubit.loadHierarchyData();

      // Get sample agents
      final sampleAgents = cubit.getSampleAgents();
      final differentAgent = sampleAgents.firstWhere(
        (agent) => agent.id != 'agent_4_1', // Different from default
      );

      // Select a different agent
      cubit.selectAgent(differentAgent);

      final state = cubit.state as NetworkWidgetLoaded;
      expect(state.selectedAgent.id, equals(differentAgent.id));
      expect(state.selectedAgent.name, equals(differentAgent.name));
    });
  });
}
