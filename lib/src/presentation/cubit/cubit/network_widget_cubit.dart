import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import '../../../domain/models/agent.dart';
import '../../../domain/models/broker.dart';

part 'network_widget_state.dart';

class NetworkWidgetCubit extends Cubit<NetworkWidgetState> {
  NetworkWidgetCubit() : super(NetworkWidgetInitial());

  void loadHierarchyData() {
    emit(NetworkWidgetLoading());

    try {
      // Create sample hardcoded hierarchical data
      final sampleData = _createSampleHierarchyData();
      emit(
        NetworkWidgetLoaded(
          broker: sampleData['broker'] as Broker,
          hierarchyData: sampleData['hierarchy'] as List<HierarchyLevel>,
          expandedLevels: {},
        ),
      );
    } catch (e) {
      emit(NetworkWidgetError('Failed to load hierarchy data: $e'));
    }
  }

  void toggleLevelExpansion(int levelIndex) {
    final currentState = state;
    if (currentState is NetworkWidgetLoaded) {
      final expandedLevels = Set<int>.from(currentState.expandedLevels);

      if (expandedLevels.contains(levelIndex)) {
        expandedLevels.remove(levelIndex);
      } else {
        expandedLevels.add(levelIndex);
      }

      emit(currentState.copyWith(expandedLevels: expandedLevels));
    }
  }

  Map<String, dynamic> _createSampleHierarchyData() {
    // Create sample broker
    final broker = Broker(
      id: 'broker_1',
      name: 'Charlotte Anderson',
      sales: 150,
      totalSalesRevenue: 2500000.0,
      totalCommission: 125000.0,
      imageUrl: 'assets/icons/agent_round.png',
      contact: '(415) 555-0101',
      email: '<EMAIL>',
      agents: [],
      color: const Color(0xFF8B1538),
      joinDate: DateTime(2022, 3, 15),
      role: 'Broker',
    );

    // Create hierarchical levels
    final hierarchyLevels = <HierarchyLevel>[
      // Level 1 - Direct agents under broker
      HierarchyLevel(
        level: 1,
        agents: [
          _createAgent(
            'agent_1_1',
            'Sophia Turner',
            '<EMAIL>',
            '(225) 555-0101',
            25,
          ),
          _createAgent(
            'agent_1_2',
            'Michael Johnson',
            '<EMAIL>',
            '(225) 555-0102',
            18,
          ),
          _createAgent(
            'agent_1_3',
            'Emma Davis',
            '<EMAIL>',
            '(225) 555-0103',
            22,
          ),
        ],
        totalAgents: 3,
      ),
      // Level 2 - Agents under level 1 agents
      HierarchyLevel(
        level: 2,
        agents: [
          _createAgent(
            'agent_2_1',
            'James Wilson',
            '<EMAIL>',
            '(225) 555-0201',
            15,
          ),
          _createAgent(
            'agent_2_2',
            'Sarah Brown',
            '<EMAIL>',
            '(225) 555-0202',
            12,
          ),
          _createAgent(
            'agent_2_3',
            'David Miller',
            '<EMAIL>',
            '(225) 555-0203',
            20,
          ),
          _createAgent(
            'agent_2_4',
            'Lisa Garcia',
            '<EMAIL>',
            '(225) 555-0204',
            16,
          ),
          _createAgent(
            'agent_2_5',
            'Robert Taylor',
            '<EMAIL>',
            '(225) 555-0205',
            14,
          ),
        ],
        totalAgents: 5,
      ),
      // Level 3 - Agents under level 2 agents
      HierarchyLevel(
        level: 3,
        agents: [
          _createAgent(
            'agent_3_1',
            'Jennifer Martinez',
            '<EMAIL>',
            '(225) 555-0301',
            8,
          ),
          _createAgent(
            'agent_3_2',
            'Christopher Lee',
            '<EMAIL>',
            '(225) 555-0302',
            10,
          ),
          _createAgent(
            'agent_3_3',
            'Amanda Rodriguez',
            '<EMAIL>',
            '(225) 555-0303',
            7,
          ),
          _createAgent(
            'agent_3_4',
            'Kevin Anderson',
            '<EMAIL>',
            '(225) 555-0304',
            9,
          ),
          _createAgent(
            'agent_3_5',
            'Michelle Thomas',
            '<EMAIL>',
            '(225) 555-0305',
            11,
          ),
          _createAgent(
            'agent_3_6',
            'Daniel Jackson',
            '<EMAIL>',
            '(225) 555-0306',
            6,
          ),
          _createAgent(
            'agent_3_7',
            'Ashley White',
            '<EMAIL>',
            '(225) 555-0307',
            13,
          ),
        ],
        totalAgents: 7,
      ),
      // Level 4 - Final level agents
      HierarchyLevel(
        level: 4,
        agents: [
          _createAgent(
            'agent_4_1',
            'Ryan Harris',
            '<EMAIL>',
            '(225) 555-0401',
            5,
          ),
          _createAgent(
            'agent_4_2',
            'Nicole Clark',
            '<EMAIL>',
            '(225) 555-0402',
            4,
          ),
          _createAgent(
            'agent_4_3',
            'Brandon Lewis',
            '<EMAIL>',
            '(225) 555-0403',
            6,
          ),
          _createAgent(
            'agent_4_4',
            'Stephanie Walker',
            '<EMAIL>',
            '(225) 555-0404',
            3,
          ),
        ],
        totalAgents: 4,
      ),
    ];

    return {'broker': broker, 'hierarchy': hierarchyLevels};
  }

  Agent _createAgent(
    String id,
    String name,
    String email,
    String contact,
    int totalAgents,
  ) {
    return Agent(
      id: id,
      name: name,
      sales: (totalAgents * 2.5).round(),
      amount: totalAgents * 15000.0,
      commission: totalAgents * 2250.0,
      contact: contact,
      email: email,
      agents: [],
      color: const Color(0xFF1A71DB),
      imageUrl: 'assets/icons/agent_round.png',
      joinDate: DateTime.now().subtract(Duration(days: (totalAgents * 30))),
      state: 'California',
      city: 'San Francisco',
      level: 'Level ${id.split('_')[1]}',
      totalDeals: totalAgents * 2,
      earning: totalAgents * 3500.0,
      status: true,
      totalAgents: totalAgents,
      soldHomes: totalAgents,
      totalSales: totalAgents * 2,
      role: 'Agent',
    );
  }
}
