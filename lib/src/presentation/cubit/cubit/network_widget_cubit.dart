import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import '../../../domain/models/agent.dart';
import '../../../domain/models/broker.dart';

part 'network_widget_state.dart';

class NetworkWidgetCubit extends Cubit<NetworkWidgetState> {
  NetworkWidgetCubit() : super(NetworkWidgetInitial());

  void loadHierarchyData() {
    emit(NetworkWidgetLoading());

    try {
      // Create sample hardcoded hierarchical data
      final sampleData = _createSampleHierarchyData();
      // Start with a selected agent (deepest level agent)
      final selectedAgent = sampleData['selectedAgent'] as Agent;
      final parentChain = sampleData['parentChain'] as List<Agent>;

      emit(
        NetworkWidgetLoaded(
          broker: sampleData['broker'] as Broker,
          selectedAgent: selectedAgent,
          immediateParent: parentChain.isNotEmpty ? parentChain.first : null,
          parentChain: parentChain,
          isExpanded: false,
          collapsedLevelsCount:
              parentChain.length - 1, // Exclude immediate parent
        ),
      );
    } catch (e) {
      emit(NetworkWidgetError('Failed to load hierarchy data: $e'));
    }
  }

  void selectAgent(Agent agent) {
    final currentState = state;
    if (currentState is NetworkWidgetLoaded) {
      // Create parent chain for the selected agent
      final parentChain = _createParentChain(agent);

      emit(
        currentState.copyWith(
          selectedAgent: agent,
          immediateParent: parentChain.isNotEmpty ? parentChain.first : null,
          parentChain: parentChain,
          isExpanded: false,
          collapsedLevelsCount: parentChain.length - 1,
        ),
      );
    }
  }

  void toggleHierarchyExpansion() {
    final currentState = state;
    if (currentState is NetworkWidgetLoaded) {
      emit(currentState.copyWith(isExpanded: !currentState.isExpanded));
    }
  }

  List<Agent> _createParentChain(Agent selectedAgent) {
    // This would normally come from your data source
    // For now, create a mock parent chain based on agent ID
    final parentChain = <Agent>[];

    // Create mock parent chain (this should be replaced with actual data logic)
    if (selectedAgent.id.contains('4_')) {
      // Level 4 agent - create chain up to broker
      parentChain.add(
        _createAgent(
          'agent_3_1',
          'Jennifer Martinez',
          '<EMAIL>',
          '(225) 555-0301',
          8,
        ),
      );
      parentChain.add(
        _createAgent(
          'agent_2_1',
          'James Wilson',
          '<EMAIL>',
          '(225) 555-0201',
          15,
        ),
      );
      parentChain.add(
        _createAgent(
          'agent_1_1',
          'Sophia Turner',
          '<EMAIL>',
          '(225) 555-0101',
          25,
        ),
      );
    } else if (selectedAgent.id.contains('3_')) {
      // Level 3 agent
      parentChain.add(
        _createAgent(
          'agent_2_1',
          'James Wilson',
          '<EMAIL>',
          '(225) 555-0201',
          15,
        ),
      );
      parentChain.add(
        _createAgent(
          'agent_1_1',
          'Sophia Turner',
          '<EMAIL>',
          '(225) 555-0101',
          25,
        ),
      );
    } else if (selectedAgent.id.contains('2_')) {
      // Level 2 agent
      parentChain.add(
        _createAgent(
          'agent_1_1',
          'Sophia Turner',
          '<EMAIL>',
          '(225) 555-0101',
          25,
        ),
      );
    }

    return parentChain;
  }

  Map<String, dynamic> _createSampleHierarchyData() {
    // Create sample broker
    final broker = Broker(
      id: 'broker_1',
      name: 'Charlotte Anderson',
      sales: 150,
      totalSalesRevenue: 2500000.0,
      totalCommission: 125000.0,
      imageUrl: 'assets/icons/agent_round.png',
      contact: '(415) 555-0101',
      email: '<EMAIL>',
      agents: [],
      color: const Color(0xFF8B1538),
      joinDate: DateTime(2022, 3, 15),
      role: 'Broker',
    );

    // Create a selected agent (deepest level)
    final selectedAgent = _createAgent(
      'agent_4_1',
      'Ryan Harris',
      '<EMAIL>',
      '(225) 555-0401',
      5,
    );

    // Create parent chain for the selected agent
    final parentChain = _createParentChain(selectedAgent);

    return {
      'broker': broker,
      'selectedAgent': selectedAgent,
      'parentChain': parentChain,
    };
  }

  // Method to get sample agents for selection
  List<Agent> getSampleAgents() {
    return [
      _createAgent(
        'agent_1_1',
        'Sophia Turner',
        '<EMAIL>',
        '(225) 555-0101',
        25,
      ),
      _createAgent(
        'agent_2_1',
        'James Wilson',
        '<EMAIL>',
        '(225) 555-0201',
        15,
      ),
      _createAgent(
        'agent_3_1',
        'Jennifer Martinez',
        '<EMAIL>',
        '(225) 555-0301',
        8,
      ),
      _createAgent(
        'agent_4_1',
        'Ryan Harris',
        '<EMAIL>',
        '(225) 555-0401',
        5,
      ),
      _createAgent(
        'agent_4_2',
        'Nicole Clark',
        '<EMAIL>',
        '(225) 555-0402',
        4,
      ),
    ];
  }

  Agent _createAgent(
    String id,
    String name,
    String email,
    String contact,
    int totalAgents,
  ) {
    return Agent(
      id: id,
      name: name,
      sales: (totalAgents * 2.5).round(),
      amount: totalAgents * 15000.0,
      commission: totalAgents * 2250.0,
      contact: contact,
      email: email,
      agents: [],
      color: const Color(0xFF1A71DB),
      imageUrl: 'assets/icons/agent_round.png',
      joinDate: DateTime.now().subtract(Duration(days: (totalAgents * 30))),
      state: 'California',
      city: 'San Francisco',
      level: 'Level ${id.split('_')[1]}',
      totalDeals: totalAgents * 2,
      earning: totalAgents * 3500.0,
      status: true,
      totalAgents: totalAgents,
      soldHomes: totalAgents,
      totalSales: totalAgents * 2,
      role: 'Agent',
    );
  }
}
