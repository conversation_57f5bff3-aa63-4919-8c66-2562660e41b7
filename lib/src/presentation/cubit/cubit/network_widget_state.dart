part of 'network_widget_cubit.dart';

@immutable
sealed class NetworkWidgetState {}

final class NetworkWidgetInitial extends NetworkWidgetState {}

final class NetworkWidgetLoading extends NetworkWidgetState {}

final class NetworkWidgetLoaded extends NetworkWidgetState {
  final Broker broker;
  final List<HierarchyLevel> hierarchyData;
  final Set<int> expandedLevels;

  NetworkWidgetLoaded({
    required this.broker,
    required this.hierarchyData,
    required this.expandedLevels,
  });

  NetworkWidgetLoaded copyWith({
    Broker? broker,
    List<HierarchyLevel>? hierarchyData,
    Set<int>? expandedLevels,
  }) {
    return NetworkWidgetLoaded(
      broker: broker ?? this.broker,
      hierarchyData: hierarchyData ?? this.hierarchyData,
      expandedLevels: expandedLevels ?? this.expandedLevels,
    );
  }
}

final class NetworkWidgetError extends NetworkWidgetState {
  final String message;

  NetworkWidgetError(this.message);
}

// Helper class to represent hierarchy levels
class HierarchyLevel {
  final int level;
  final List<Agent> agents;
  final int totalAgents;

  const HierarchyLevel({
    required this.level,
    required this.agents,
    required this.totalAgents,
  });
}
