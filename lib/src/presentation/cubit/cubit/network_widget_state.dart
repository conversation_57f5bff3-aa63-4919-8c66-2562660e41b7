part of 'network_widget_cubit.dart';

@immutable
sealed class NetworkWidgetState {}

final class NetworkWidgetInitial extends NetworkWidgetState {}

final class NetworkWidgetLoading extends NetworkWidgetState {}

final class NetworkWidgetLoaded extends NetworkWidgetState {
  final Broker broker;
  final Agent selectedAgent;
  final Agent? immediateParent;
  final List<Agent> parentChain;
  final bool isExpanded;
  final int collapsedLevelsCount;

  NetworkWidgetLoaded({
    required this.broker,
    required this.selectedAgent,
    required this.immediateParent,
    required this.parent<PERSON>hain,
    required this.isExpanded,
    required this.collapsedLevelsCount,
  });

  NetworkWidgetLoaded copyWith({
    Broker? broker,
    Agent? selectedAgent,
    Agent? immediateParent,
    List<Agent>? parentChain,
    bool? isExpanded,
    int? collapsedLevelsCount,
  }) {
    return NetworkWidgetLoaded(
      broker: broker ?? this.broker,
      selectedAgent: selectedAgent ?? this.selectedAgent,
      immediateParent: immediateParent ?? this.immediateParent,
      parentChain: parentChain ?? this.parent<PERSON>hain,
      isExpanded: isExpanded ?? this.isExpanded,
      collapsedLevelsCount: collapsedLevelsCount ?? this.collapsedLevelsCount,
    );
  }
}

final class NetworkWidgetError extends NetworkWidgetState {
  final String message;

  NetworkWidgetError(this.message);
}
