// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import '../../../core/config/app_strings.dart' as AppStrings;
// import '../../../core/config/constants.dart';
// import '../../../core/config/responsive.dart';
// import '../../../core/enum/user_role.dart';
// import '../../../core/theme/app_fonts.dart';
// import '../../../core/theme/app_theme.dart';
// import '../../../domain/models/agent.dart';
// import '../../../domain/models/broker.dart';
// import '../../../domain/models/user.dart';
// import '../../cubit/cubit/agent_network_cubit.dart';
// import '../../cubit/user/user_cubit.dart';
// import 'components/agent_hierarchy_breadcrumb.dart';
// import 'components/agent_network_card.dart';
// import 'components/agent_recruits_section.dart';

// class AgentNetworkScreen extends HookWidget {
//   final String selectedBrokerId;
//   final bool showScaffold;

//   const AgentNetworkScreen({
//     super.key,
//     required this.selectedBrokerId,
//     this.showScaffold = true,
//   });

//   @override
//   Widget build(BuildContext context) {
//     if (showScaffold) {
//       return _buildWithScaffold(context);
//     } else {
//       return _buildContent(context);
//     }
//   }

//   Widget _buildWithScaffold(BuildContext context) {
//     final bool isMobile = Responsive.isMobile(context);
//     final user = context.watch<UserCubit>().state.user;

//     return Scaffold(
//       body: SafeArea(
//         child: Container(
//           padding: EdgeInsets.fromLTRB(
//             isMobile ? 8 : webLayoutmargin,
//             isMobile ? 8 : defaultMargin,
//             isMobile ? 8 : webLayoutmargin,
//             0,
//           ),
//           child: SizedBox(
//             height: MediaQuery.of(context).size.height, // Constrain height
//             child: Column(children: [_buildContent(context)]),
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildContent(BuildContext context) {
//     final size = MediaQuery.of(context).size;
//     final showAllRecruits = useState<bool>(false);
//     final scrollController = useRef<ScrollController?>(null);

//     useEffect(() {
//       scrollController.value = ScrollController();
//       return () => scrollController.value?.dispose();
//     }, []);

//     useEffect(() {
//       // Reset hierarchy and fetch root level data
//       context.read<AgentNetworkCubit>().resetHierarchy();
//       context.read<AgentNetworkCubit>().fetchNetworkItems(
//         selectedBrokerId,
//         isRoot: true,
//       );
//       return null;
//     }, [selectedBrokerId]);

//     void navigateToAgent(Agent agent) {
//       context.read<AgentNetworkCubit>().navigateToAgent(agent);
//       showAllRecruits.value = false; // Reset on agent change
//       // Auto-scroll to show the new agent and recruits with better positioning
//       scrollHandler(scrollController);
//     }

//     void navigateToLevel(int level) {
//       if (level == 0) {
//         context.read<AgentNetworkCubit>().resetHierarchy();
//         context.read<AgentNetworkCubit>().fetchNetworkItems(
//           selectedBrokerId,
//           isRoot: true,
//         );
//       } else {
//         context.read<AgentNetworkCubit>().navigateToLevel(level - 1);
//       }
//       showAllRecruits.value = false;
//       scrollHandler(scrollController);
//     }

//     return SizedBox(
//       height: size.height, // Constrain height
//       child: BlocBuilder<AgentNetworkCubit, AgentNetworkState>(
//         builder: (context, state) {
//           debugPrint('State: $state');

//           return AnimatedSwitcher(
//             duration: const Duration(milliseconds: 300),
//             transitionBuilder: (Widget child, Animation<double> animation) {
//               return FadeTransition(
//                 opacity: animation,
//                 child: SlideTransition(
//                   position:
//                       Tween<Offset>(
//                         begin: const Offset(0.0, 0.02),
//                         end: Offset.zero,
//                       ).animate(
//                         CurvedAnimation(
//                           parent: animation,
//                           curve: Curves.easeOutCubic,
//                         ),
//                       ),
//                   child: child,
//                 ),
//               );
//             },
//             child: _buildStableContent(
//               context,
//               state,
//               navigateToAgent,
//               navigateToLevel,
//               showAllRecruits,
//               size,
//               scrollController.value,
//             ),
//           );
//         },
//       ),
//     );
//   }

//   void scrollHandler(ObjectRef<ScrollController?> scrollController) {
//     Future.delayed(const Duration(milliseconds: 600), () {
//       if (scrollController.value != null &&
//           scrollController.value!.hasClients) {
//         final maxScroll = scrollController.value!.position.maxScrollExtent;
//         final viewportHeight =
//             scrollController.value!.position.viewportDimension;

//         // Calculate target scroll position to show recruits section properly
//         // Position so that the recruits section is visible with some padding
//         final recruitsSectionHeight =
//             viewportHeight * 0.6; // Estimate recruits section height

//         final targetScroll = maxScroll;
//         //- recruitsSectionHeight;

//         // Ensure we don't scroll past the maximum or below zero
//         final finalTargetScroll = targetScroll.clamp(0.0, maxScroll);

//         scrollController.value!.animateTo(
//           finalTargetScroll,
//           duration: const Duration(milliseconds: 500),
//           curve: Curves.easeOutCubic,
//         );
//       }
//     });
//   }

//   Widget _buildStableContent(
//     BuildContext context,
//     AgentNetworkState state,
//     Function(Agent) navigateToAgent,
//     Function(int) navigateToLevel,
//     ValueNotifier<bool> showAllRecruits,
//     Size size,
//     ScrollController? scrollController,
//   ) {
//     // Use the current state directly - the buildWhen condition handles stability
//     final workingState = state;
//     if (workingState is AgentNetworkLoading) {
//       return Container(
//         key: const ValueKey('loading'),
//         child: const Center(child: CircularProgressIndicator()),
//       );
//     } else if (workingState is AgentNetworkError) {
//       return Container(
//         key: const ValueKey('error'),
//         child: Center(
//           child: Text('Failed to load agents: ${workingState.message}'),
//         ),
//       );
//     } else if (workingState is AgentNetworkSuccess) {
//       final selectedAgent = workingState.agent;
//       final broker = workingState.broker;
//       final hierarchyPath = workingState.hierarchyPath;

//       if (selectedAgent == null) {
//         return Container(
//           key: const ValueKey('no-data'),
//           child: const Center(child: Text('No agent data available')),
//         );
//       }

//       return Container(
//         key: ValueKey(
//           'success-${selectedAgent.id}',
//         ), // Unique key for each agent
//         child: Column(
//           children: [
//             // Breadcrumb with smooth animation
//             AnimatedContainer(
//               duration: const Duration(milliseconds: 200),
//               child: AgentHierarchyBreadcrumb(
//                 hierarchyPath: hierarchyPath,
//                 onNavigate: navigateToLevel,
//                 broker: broker,
//               ),
//             ),
//             const SizedBox(height: defaultPadding),
//             Text(
//               AppStrings.agentNetwork,
//               style: AppFonts.semiBoldTextStyle(
//                 24,
//                 color: AppTheme.primaryTextColor,
//               ),
//             ),
//             const SizedBox(height: defaultPadding),
//             // Wrap in Expanded to prevent overflow with deep hierarchies
//             Expanded(
//               child: _networkView(
//                 context,
//                 hierarchyPath,
//                 size,
//                 selectedAgent,
//                 showAllRecruits,
//                 scrollController,
//                 navigateToAgent,
//                 user: context.watch<UserCubit>().state.user,
//                 // Enable expanded mode for proper scrolling
//               ),
//             ),
//           ],
//         ),
//       );
//     }

//     return Container(
//       key: const ValueKey('initializing'),
//       child: const Center(child: Text('Initializing...')),
//     );
//   }

//   Widget _networkView(
//     BuildContext context,
//     List<Agent> hierarchyPath,
//     Size size,
//     Agent? agent,
//     ValueNotifier<bool> showAllRecruits,
//     ScrollController? scrollController,
//     void Function(Agent agent) navigateToAgent, {
//     bool useExpanded = false,
//     required User? user,
//   }) {
//     final isSmallMobile = Responsive.isSmallMobile(context);
//     final isMobile = Responsive.isMobile(context);
//     final isTablet = Responsive.isTablet(context);

//     // Calculate available height for deep hierarchies
//     final availableHeight =
//         size.height - 200; // Reserve space for header/breadcrumb
//     final hierarchyDepth = hierarchyPath.length;
//     final isDeepHierarchy = hierarchyDepth > 3;

//     final content = SingleChildScrollView(
//       controller: scrollController,
//       physics: const BouncingScrollPhysics(),
//       child: ConstrainedBox(
//         constraints: BoxConstraints(
//           minHeight: isDeepHierarchy ? availableHeight : 0,
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.center,
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             if (hierarchyPath.isEmpty)
//               _buildAgentCard(
//                 context: context,
//                 agent: agent,
//                 isMainCard: true,

//                 isBrokerCard:
//                     !(agent!.role == UserRole.admin ||
//                         agent!.role == UserRole.platformOwner),
//                 useIntrinsic: true,
//               ),

//             if (hierarchyPath.isNotEmpty)
//               _buildAgentCard(
//                 context: context,
//                 agent: agent,
//                 isMainCard: false,
//                 isBrokerCard:
//                     !(agent!.role == UserRole.admin ||
//                         agent!.role == UserRole.platformOwner),

//                 width: isTablet
//                     ? size.width / 2.5
//                     : isMobile
//                     ? isSmallMobile
//                           ? size.width
//                           : size.width / 1.8
//                     : size.width / 4,
//                 height: 100,
//               ),

//             _networkline(),

//             if (agent != null) ...[
//               for (int index = 0; index < hierarchyPath.length; index++)
//                 _AnimatedHierarchyCard(
//                   key: ValueKey('hierarchy-${hierarchyPath[index].id}'),
//                   agent: hierarchyPath[index],
//                   isMainCard: index == hierarchyPath.length - 1,
//                   size: size,
//                   animationDelay: Duration(milliseconds: index * 100),
//                 ),

//               ValueListenableBuilder<bool>(
//                 valueListenable: showAllRecruits,
//                 builder: (context, showAll, _) {
//                   return AnimatedContainer(
//                     duration: const Duration(milliseconds: 400),
//                     curve: Curves.easeOutCubic,
//                     child: _AnimatedAgentRecruitsSection(
//                       key: ValueKey('recruits-${agent?.id}'),
//                       isBrokerLevel: hierarchyPath.isEmpty,
//                       broker:
//                           context.read<AgentNetworkCubit>().state
//                               is AgentNetworkSuccess
//                           ? (context.read<AgentNetworkCubit>().state
//                                     as AgentNetworkSuccess)
//                                 .broker
//                           : null,
//                       parentAgent: agent,
//                       onAgentTap: navigateToAgent,
//                       showAll: showAll,
//                       onViewMore: () {
//                         showAllRecruits.value = !showAllRecruits.value;
//                         debugPrint(
//                           'showAllRecruits toggled to: ${showAllRecruits.value}',
//                         );
//                       },
//                     ),
//                   );
//                 },
//               ),
//             ],
//           ],
//         ),
//       ),
//     );

//     return content;
//   }

//   Widget _buildAgentCard({
//     required BuildContext context,
//     required Agent? agent,
//     required bool isMainCard,
//     required bool isBrokerCard,
//     double? width,
//     double? height,
//     bool useIntrinsic = false,
//   }) {
//     final card = AgentNetworkCard(
//       broker: context.read<AgentNetworkCubit>().state is AgentNetworkSuccess
//           ? (context.read<AgentNetworkCubit>().state as AgentNetworkSuccess)
//                 .broker
//           : null,
//       agent: agent,
//       isMainCard: isMainCard,
//       isBrokerCard: isBrokerCard,
//     );

//     if (useIntrinsic) {
//       return IntrinsicWidth(child: card);
//     }

//     if (width != null || height != null) {
//       return SizedBox(width: width, height: height, child: card);
//     }

//     return card;
//   }

//   Container _networkline() {
//     return Container(height: 30, width: 1, color: AppTheme.hierarchyLineColor);
//   }
// }

// // Animated hierarchy card for smooth hierarchy transitions
// class _AnimatedHierarchyCard extends StatefulWidget {
//   final Agent agent;
//   final bool isMainCard;
//   final Size size;
//   final Duration animationDelay;

//   const _AnimatedHierarchyCard({
//     super.key,
//     required this.agent,
//     required this.isMainCard,
//     required this.size,
//     this.animationDelay = Duration.zero,
//   });

//   @override
//   State<_AnimatedHierarchyCard> createState() => _AnimatedHierarchyCardState();
// }

// class _AnimatedHierarchyCardState extends State<_AnimatedHierarchyCard>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _controller;
//   late Animation<double> _fadeAnimation;
//   late Animation<Offset> _slideAnimation;
//   late Animation<double> _scaleAnimation;

//   @override
//   void initState() {
//     super.initState();
//     _setupAnimations();
//     _startAnimation();
//   }

//   void _setupAnimations() {
//     _controller = AnimationController(
//       duration: const Duration(milliseconds: 600),
//       vsync: this,
//     );

//     _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
//       CurvedAnimation(
//         parent: _controller,
//         curve: const Interval(0.0, 0.8, curve: Curves.easeOutCubic),
//       ),
//     );

//     _slideAnimation =
//         Tween<Offset>(begin: const Offset(0.0, 0.3), end: Offset.zero).animate(
//           CurvedAnimation(
//             parent: _controller,
//             curve: const Interval(0.0, 0.9, curve: Curves.easeOutQuart),
//           ),
//         );

//     _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
//       CurvedAnimation(
//         parent: _controller,
//         curve: const Interval(0.2, 1.0, curve: Curves.easeOutBack),
//       ),
//     );
//   }

//   void _startAnimation() {
//     Future.delayed(widget.animationDelay, () {
//       if (mounted) {
//         _controller.forward();
//       }
//     });
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final isSmallMobile = Responsive.isSmallMobile(context);
//     final isMobile = Responsive.isMobile(context);
//     final isTablet = Responsive.isTablet(context);

//     // Make cards more compact for deep hierarchies
//     final isDeepHierarchy =
//         widget.animationDelay.inMilliseconds > 300; // More than 3 levels
//     final cardHeight = isDeepHierarchy
//         ? 80.0
//         : (widget.isMainCard ? null : 100.0);
//     final lineHeight = isDeepHierarchy ? 20.0 : 30.0;

//     return AnimatedBuilder(
//       animation: _controller,
//       builder: (context, child) {
//         return FadeTransition(
//           opacity: _fadeAnimation,
//           child: SlideTransition(
//             position: _slideAnimation,
//             child: ScaleTransition(
//               scale: _scaleAnimation,
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   _buildAgentCard(
//                     context: context,
//                     agent: widget.agent,
//                     isMainCard: widget.isMainCard,
//                     isBrokerCard: false,
//                     useIntrinsic: widget.isMainCard,
//                     width: !widget.isMainCard ? widget.size.width / 4 : null,
//                     height: cardHeight,
//                   ),
//                   Container(
//                     height: lineHeight,
//                     width: 1,
//                     color: AppTheme.hierarchyLineColor,
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildAgentCard({
//     required BuildContext context,
//     required Agent? agent,
//     required bool isMainCard,
//     required bool isBrokerCard,
//     double? width,
//     double? height,
//     bool useIntrinsic = false,
//   }) {
//     final card = AgentNetworkCard(
//       broker: context.read<AgentNetworkCubit>().state is AgentNetworkSuccess
//           ? (context.read<AgentNetworkCubit>().state as AgentNetworkSuccess)
//                 .broker
//           : null,
//       agent: agent,
//       isMainCard: isMainCard,
//       isBrokerCard: isBrokerCard,
//     );

//     if (useIntrinsic) {
//       return IntrinsicWidth(child: card);
//     }

//     if (width != null || height != null) {
//       return SizedBox(width: width, height: height, child: card);
//     }

//     return card;
//   }
// }

// // Animated wrapper for AgentRecruitsSection to handle smooth transitions
// class _AnimatedAgentRecruitsSection extends StatefulWidget {
//   final bool isBrokerLevel;
//   final Broker? broker;
//   final Agent? parentAgent;
//   final Function(Agent) onAgentTap;
//   final bool showAll;
//   final VoidCallback? onViewMore;

//   const _AnimatedAgentRecruitsSection({
//     super.key,
//     required this.isBrokerLevel,
//     required this.parentAgent,
//     required this.onAgentTap,
//     this.showAll = false,
//     this.onViewMore,
//     required this.broker,
//   });

//   @override
//   State<_AnimatedAgentRecruitsSection> createState() =>
//       _AnimatedAgentRecruitsSectionState();
// }

// class _AnimatedAgentRecruitsSectionState
//     extends State<_AnimatedAgentRecruitsSection>
//     with TickerProviderStateMixin {
//   late AnimationController _slideController;
//   late AnimationController _fadeController;
//   late Animation<Offset> _slideAnimation;
//   late Animation<double> _fadeAnimation;
//   late Animation<double> _scaleAnimation;

//   @override
//   void initState() {
//     super.initState();
//     _setupAnimations();
//     _startAnimations();
//   }

//   void _setupAnimations() {
//     _slideController = AnimationController(
//       duration: const Duration(milliseconds: 500),
//       vsync: this,
//     );

//     _fadeController = AnimationController(
//       duration: const Duration(milliseconds: 400),
//       vsync: this,
//     );

//     _slideAnimation =
//         Tween<Offset>(begin: const Offset(0.0, 0.1), end: Offset.zero).animate(
//           CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
//         );

//     _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
//       CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
//     );

//     _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
//       CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
//     );
//   }

//   void _startAnimations() {
//     _fadeController.forward();
//     _slideController.forward();
//   }

//   @override
//   void didUpdateWidget(_AnimatedAgentRecruitsSection oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (oldWidget.parentAgent?.id != widget.parentAgent?.id) {
//       // Reset and restart animations when parent agent changes
//       _fadeController.reset();
//       _slideController.reset();
//       _startAnimations();
//     }
//   }

//   @override
//   void dispose() {
//     _slideController.dispose();
//     _fadeController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     if (widget.parentAgent == null) {
//       return const SizedBox.shrink();
//     }

//     return AnimatedBuilder(
//       animation: Listenable.merge([_slideController, _fadeController]),
//       builder: (context, child) {
//         return FadeTransition(
//           opacity: _fadeAnimation,
//           child: SlideTransition(
//             position: _slideAnimation,
//             child: ScaleTransition(
//               scale: _scaleAnimation,
//               child: AgentRecruitsSection(
//                 isBrokerLevel: widget.isBrokerLevel,
//                 broker: widget.broker,
//                 parentAgent: widget.parentAgent!,
//                 onAgentTap: _handleAgentTap,
//                 showAll: widget.showAll,
//                 onViewMore: widget.onViewMore,
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }

//   void _handleAgentTap(Agent agent) {
//     // Add a subtle animation before navigating
//     _fadeController.reverse().then((_) {
//       widget.onAgentTap(agent);
//     });
//   }
// }

//need an empty AgentNetworkScreen for implimenting other logic

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import '../../cubit/cubit/network_widget_cubit.dart';
import 'components/agent_network_card.dart';

class AgentNetworkScreen extends StatelessWidget {
  final String selectedBrokerId;
  final bool showScaffold;

  const AgentNetworkScreen({
    required this.selectedBrokerId,
    this.showScaffold = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => NetworkWidgetCubit()..loadHierarchyData(),
      child: showScaffold
          ? _buildWithScaffold(context)
          : _buildContent(context),
    );
  }

  Widget _buildWithScaffold(BuildContext context) {
    final bool isMobile = Responsive.isMobile(context);

    return Scaffold(
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.fromLTRB(
            isMobile ? 8 : webLayoutmargin,
            isMobile ? 8 : defaultMargin,
            isMobile ? 8 : webLayoutmargin,
            0,
          ),
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            child: Column(children: [_buildContent(context)]),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Expanded(
      child: BlocBuilder<NetworkWidgetCubit, NetworkWidgetState>(
        builder: (context, state) {
          if (state is NetworkWidgetLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is NetworkWidgetError) {
            return Center(
              child: Text(
                'Error: ${state.message}',
                style: AppFonts.mediumTextStyle(16, color: Colors.red),
              ),
            );
          } else if (state is NetworkWidgetLoaded) {
            return _buildHierarchyView(context, state);
          }

          return const Center(child: Text('Initializing...'));
        },
      ),
    );
  }

  Widget _buildHierarchyView(BuildContext context, NetworkWidgetLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(defaultPadding),
      child: Column(
        children: [
          // Title
          Text(
            'Agent Network Hierarchy',
            style: AppFonts.semiBoldTextStyle(
              24,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: defaultPadding * 2),

          // Broker Card (Top Level)
          AgentNetworkCard(
            broker: state.broker,
            agent: null,
            isMainCard: true,
            isBrokerCard: true,
          ),

          // Hierarchy Chain
          ...state.hierarchyData.asMap().entries.map((entry) {
            final index = entry.key;
            final level = entry.value;
            final isExpanded = state.expandedLevels.contains(index);
            final isLastLevel = index == state.hierarchyData.length - 1;

            return Column(
              children: [
                // Dotted line
                _buildDottedLine(),

                // Level indicator with count (clickable circle)
                _buildLevelIndicator(
                  context,
                  level,
                  index,
                  isExpanded,
                  isLastLevel,
                ),

                // Expanded agents (if expanded)
                if (isExpanded) ...[
                  _buildDottedLine(),
                  _buildExpandedAgents(context, level),
                ],

                // Continue dotted line if not expanded and not last level
                if (!isExpanded && !isLastLevel) _buildDottedLine(),
              ],
            );
          }).toList(),

          // Final agent card (last level)
          if (state.hierarchyData.isNotEmpty) ...[
            _buildDottedLine(),
            _buildFinalAgentCard(context, state.hierarchyData.last),
          ],
        ],
      ),
    );
  }

  Widget _buildDottedLine() {
    return SizedBox(
      height: 40,
      child: Column(
        children: List.generate(8, (index) {
          return Expanded(
            child: Container(
              width: 2,
              margin: const EdgeInsets.symmetric(vertical: 1),
              decoration: BoxDecoration(
                color: AppTheme.hierarchyLineColor.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildLevelIndicator(
    BuildContext context,
    HierarchyLevel level,
    int index,
    bool isExpanded,
    bool isLastLevel,
  ) {
    return GestureDetector(
      onTap: () {
        context.read<NetworkWidgetCubit>().toggleLevelExpansion(index);
      },
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: isExpanded ? AppTheme.primaryColor : AppTheme.roundIconColor,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${level.totalAgents}',
              style: AppFonts.semiBoldTextStyle(
                16,
                color: isExpanded ? Colors.white : AppTheme.primaryTextColor,
              ),
            ),
            Text(
              'Level ${level.level}',
              style: AppFonts.mediumTextStyle(
                10,
                color: isExpanded
                    ? Colors.white.withValues(alpha: 0.8)
                    : AppTheme.secondaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandedAgents(BuildContext context, HierarchyLevel level) {
    final isMobile = Responsive.isMobile(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: defaultPadding),
      child: Column(
        children: [
          // Level title
          Padding(
            padding: const EdgeInsets.symmetric(vertical: defaultPadding),
            child: Text(
              'Level ${level.level} Agents (${level.totalAgents})',
              style: AppFonts.semiBoldTextStyle(
                18,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ),

          // Agents grid/list
          if (isMobile)
            // Mobile: Single column
            Column(
              children: level.agents.map((agent) {
                return Container(
                  margin: const EdgeInsets.only(bottom: defaultPadding),
                  child: AgentNetworkCard(
                    broker: null,
                    agent: agent,
                    isMainCard: false,
                    isBrokerCard: false,
                  ),
                );
              }).toList(),
            )
          else
            // Desktop/Tablet: Grid layout
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: Responsive.isDesktop(context) ? 3 : 2,
                childAspectRatio: 2.5,
                crossAxisSpacing: defaultPadding,
                mainAxisSpacing: defaultPadding,
              ),
              itemCount: level.agents.length,
              itemBuilder: (context, index) {
                return AgentNetworkCard(
                  broker: null,
                  agent: level.agents[index],
                  isMainCard: false,
                  isBrokerCard: false,
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildFinalAgentCard(BuildContext context, HierarchyLevel lastLevel) {
    if (lastLevel.agents.isEmpty) return const SizedBox.shrink();

    // Show the first agent from the last level as the final card
    final finalAgent = lastLevel.agents.first;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: defaultPadding),
      child: Column(
        children: [
          Text(
            'Final Level Agent',
            style: AppFonts.semiBoldTextStyle(
              16,
              color: AppTheme.secondaryTextColor,
            ),
          ),
          const SizedBox(height: defaultPadding),
          AgentNetworkCard(
            broker: null,
            agent: finalAgent,
            isMainCard: true,
            isBrokerCard: false,
          ),
        ],
      ),
    );
  }
}
